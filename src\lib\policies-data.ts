import { Policy, PolicyTemplate, PolicyException, PolicyViolation, PolicyAttestation } from "@/types/policies"

// Sample policies data for demonstration
export const samplePolicies: Policy[] = [
  {
    id: "pol-001",
    title: "Information Security Policy",
    description: "Comprehensive information security policy covering data protection, access controls, and incident response procedures",
    policyType: "security",
    status: "published",
    priority: "critical",
    version: "3.2",
    
    owner: "CISO",
    ownerEmail: "<EMAIL>",
    responsibleParty: "Security Team",
    businessFunction: "Information Security",
    
    createdDate: new Date("2023-01-15T09:00:00Z"),
    lastModifiedDate: new Date("2024-01-10T14:30:00Z"),
    lastReviewDate: new Date("2024-01-05T10:00:00Z"),
    nextReviewDate: new Date("2024-07-05T10:00:00Z"),
    reviewFrequency: "semi-annually",
    expirationDate: new Date("2025-01-15T09:00:00Z"),
    
    approvals: [
      {
        id: "app-001",
        approverId: "ciso-001",
        approverName: "<PERSON>",
        approverRole: "Chief Information Security Officer",
        status: "approved",
        approvalDate: new Date("2024-01-10T16:00:00Z"),
        comments: "Approved with minor revisions to incident response procedures",
        blockchainHash: "0x1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b",
        blockchainBlock: "0x123456",
        verificationTimestamp: new Date("2024-01-10T16:05:00Z")
      }
    ],
    currentApprovalStatus: "approved",
    approvalWorkflowId: "wf-sec-001",
    
    content: "# Information Security Policy\n\n## Purpose\nThis policy establishes the framework for protecting Auris Compliance information assets...",
    summary: "Establishes comprehensive security controls for information protection, access management, and incident response",
    scope: "All employees, contractors, and third-party users accessing Auris Compliance systems and data",
    applicability: ["All Employees", "Contractors", "Third-party Users", "System Administrators"],
    attachments: [
      {
        id: "att-001",
        name: "Security Control Matrix.xlsx",
        description: "Detailed mapping of security controls to policy requirements",
        fileType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        fileSize: 245760,
        uploadDate: new Date("2024-01-10T14:00:00Z"),
        uploadedBy: "<EMAIL>",
        downloadUrl: "/policies/attachments/att-001",
        isRequired: true
      }
    ],
    relatedPolicies: ["pol-002", "pol-003", "pol-007"],
    
    applicableFrameworks: ["NIST CSF 2.0", "ISO 27001", "SOC 2"],
    mappedControls: [
      {
        id: "ctrl-001",
        controlId: "AC-1",
        frameworkId: "nist-csf-2.0",
        frameworkName: "NIST CSF 2.0",
        controlTitle: "Access Control Policy and Procedures",
        implementationStatus: "implemented",
        testingStatus: "passed",
        lastTestDate: new Date("2024-01-05T12:00:00Z"),
        nextTestDate: new Date("2024-04-05T12:00:00Z")
      }
    ],
    complianceLevel: "compliant",
    
    enforcementStatus: "fully-enforced",
    enforcementMechanism: ["OPA Policy Engine", "SIEM Integration", "Access Control Systems"],
    opaPolicy: {
      regoCode: "package auris.security\n\ndefault allow = false\n\nallow {\n  input.user.role == \"admin\"\n}",
      packageName: "auris.security.access",
      lastDeployed: new Date("2024-01-10T15:00:00Z"),
      deploymentStatus: "deployed"
    },
    
    activeExceptions: [],
    recentViolations: [
      {
        id: "viol-001",
        policyId: "pol-001",
        violationType: "technical",
        severity: "medium",
        description: "Unauthorized access attempt detected from external IP",
        detectedDate: new Date("2024-01-28T08:30:00Z"),
        detectedBy: "automated",
        affectedAssets: ["srv-web-01", "db-prod-01"],
        remediationStatus: "resolved",
        remediationDueDate: new Date("2024-01-30T17:00:00Z"),
        assignedTo: "<EMAIL>",
        resolutionDate: new Date("2024-01-29T14:20:00Z"),
        resolutionNotes: "IP blocked, access logs reviewed, no data compromise detected"
      }
    ],
    
    metrics: {
      complianceRate: 98.5,
      violationCount: 3,
      exceptionCount: 0,
      lastAssessmentDate: new Date("2024-01-05T10:00:00Z"),
      nextReviewDate: new Date("2024-07-05T10:00:00Z"),
      attestationRate: 94.2,
      enforcementEffectiveness: 96.8
    },
    
    entityId: "auris-hq",
    
    versions: [
      {
        id: "ver-001",
        version: "3.2",
        changeDescription: "Updated incident response procedures and added zero-trust requirements",
        changedBy: "<EMAIL>",
        changeDate: new Date("2024-01-10T14:30:00Z"),
        approvals: [
          {
            id: "app-001",
            approverId: "ciso-001",
            approverName: "John Smith",
            approverRole: "Chief Information Security Officer",
            status: "approved",
            approvalDate: new Date("2024-01-10T16:00:00Z"),
            comments: "Approved with minor revisions"
          }
        ],
        isActive: true,
        documentUrl: "/policies/documents/pol-001-v3.2.pdf",
        blockchainHash: "0x1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b"
      }
    ],
    
    blockchainStatus: "verified",
    blockchainHash: "0x1a2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b",
    blockchainBlock: "0x123456",
    verificationTimestamp: new Date("2024-01-10T16:05:00Z"),
    
    tags: ["security", "access-control", "incident-response", "critical"],
    customFields: {
      businessImpact: "high",
      regulatoryRequirement: true,
      auditFrequency: "quarterly"
    },
    isArchived: false,
    createdBy: "<EMAIL>",
    lastModifiedBy: "<EMAIL>"
  },
  {
    id: "pol-002",
    title: "Data Privacy and Protection Policy",
    description: "Comprehensive data privacy policy ensuring GDPR, CCPA, and other privacy regulation compliance",
    policyType: "privacy",
    status: "published",
    priority: "critical",
    version: "2.1",
    
    owner: "DPO",
    ownerEmail: "<EMAIL>",
    responsibleParty: "Privacy Team",
    businessFunction: "Data Protection",
    
    createdDate: new Date("2023-05-25T10:00:00Z"),
    lastModifiedDate: new Date("2024-01-15T11:30:00Z"),
    lastReviewDate: new Date("2024-01-10T09:00:00Z"),
    nextReviewDate: new Date("2024-04-10T09:00:00Z"),
    reviewFrequency: "quarterly",
    
    approvals: [
      {
        id: "app-002",
        approverId: "dpo-001",
        approverName: "Sarah Johnson",
        approverRole: "Data Protection Officer",
        status: "approved",
        approvalDate: new Date("2024-01-15T13:00:00Z"),
        comments: "Approved - updated for new GDPR guidance"
      }
    ],
    currentApprovalStatus: "approved",
    
    content: "# Data Privacy and Protection Policy\n\n## Purpose\nThis policy ensures compliance with data protection regulations...",
    summary: "Establishes data protection controls, privacy rights, and breach response procedures",
    scope: "All personal data processing activities within Auris Compliance",
    applicability: ["All Employees", "Data Processors", "Third-party Vendors"],
    attachments: [],
    relatedPolicies: ["pol-001", "pol-004"],
    
    applicableFrameworks: ["GDPR", "CCPA", "ISO 27001"],
    mappedControls: [],
    complianceLevel: "compliant",
    
    enforcementStatus: "fully-enforced",
    enforcementMechanism: ["Data Loss Prevention", "Access Controls", "Audit Logging"],
    
    activeExceptions: [
      {
        id: "exc-001",
        policyId: "pol-002",
        requestorId: "user-hr-001",
        requestorName: "HR Manager",
        justification: "Legacy HR system requires extended data retention for legal compliance",
        riskAssessment: "Low risk - data is encrypted and access is restricted",
        approvalStatus: "approved",
        approvedBy: "<EMAIL>",
        approvedDate: new Date("2024-01-20T14:00:00Z"),
        expirationDate: new Date("2024-06-20T14:00:00Z"),
        createdDate: new Date("2024-01-18T10:00:00Z"),
        isActive: true,
        mitigationMeasures: ["Enhanced encryption", "Quarterly access reviews", "Automated deletion after legal hold"]
      }
    ],
    recentViolations: [],
    
    metrics: {
      complianceRate: 99.1,
      violationCount: 0,
      exceptionCount: 1,
      lastAssessmentDate: new Date("2024-01-10T09:00:00Z"),
      nextReviewDate: new Date("2024-04-10T09:00:00Z"),
      attestationRate: 97.8,
      enforcementEffectiveness: 98.5
    },
    
    entityId: "auris-hq",
    versions: [],
    
    blockchainStatus: "verified",
    blockchainHash: "0x2b3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c",
    blockchainBlock: "0x234567",
    verificationTimestamp: new Date("2024-01-15T13:05:00Z"),
    
    tags: ["privacy", "gdpr", "ccpa", "data-protection"],
    isArchived: false,
    createdBy: "<EMAIL>",
    lastModifiedBy: "<EMAIL>"
  },
  {
    id: "pol-003",
    title: "Incident Response Policy",
    description: "Comprehensive incident response procedures for security incidents, data breaches, and system outages",
    policyType: "security",
    status: "published",
    priority: "high",
    version: "1.8",

    owner: "Security Manager",
    ownerEmail: "<EMAIL>",
    responsibleParty: "Incident Response Team",
    businessFunction: "Security Operations",

    createdDate: new Date("2023-03-10T08:00:00Z"),
    lastModifiedDate: new Date("2024-01-20T16:45:00Z"),
    lastReviewDate: new Date("2024-01-15T14:00:00Z"),
    nextReviewDate: new Date("2024-04-15T14:00:00Z"),
    reviewFrequency: "quarterly",

    approvals: [
      {
        id: "app-003",
        approverId: "ciso-001",
        approverName: "John Smith",
        approverRole: "Chief Information Security Officer",
        status: "approved",
        approvalDate: new Date("2024-01-20T17:30:00Z"),
        comments: "Approved - updated escalation procedures"
      }
    ],
    currentApprovalStatus: "approved",

    content: "# Incident Response Policy\n\n## Purpose\nThis policy defines the procedures for responding to security incidents...",
    summary: "Establishes incident classification, response procedures, and recovery protocols",
    scope: "All security incidents affecting Auris Compliance systems and data",
    applicability: ["Security Team", "IT Operations", "Management", "All Employees"],
    attachments: [
      {
        id: "att-002",
        name: "Incident Response Playbook.pdf",
        description: "Detailed incident response procedures and contact information",
        fileType: "application/pdf",
        fileSize: 1024000,
        uploadDate: new Date("2024-01-20T16:00:00Z"),
        uploadedBy: "<EMAIL>",
        downloadUrl: "/policies/attachments/att-002",
        isRequired: true
      }
    ],
    relatedPolicies: ["pol-001", "pol-004"],

    applicableFrameworks: ["NIST CSF 2.0", "ISO 27035", "SOC 2"],
    mappedControls: [
      {
        id: "ctrl-002",
        controlId: "IR-1",
        frameworkId: "nist-csf-2.0",
        frameworkName: "NIST CSF 2.0",
        controlTitle: "Incident Response Policy and Procedures",
        implementationStatus: "implemented",
        testingStatus: "passed",
        lastTestDate: new Date("2024-01-15T10:00:00Z"),
        nextTestDate: new Date("2024-04-15T10:00:00Z")
      }
    ],
    complianceLevel: "compliant",

    enforcementStatus: "fully-enforced",
    enforcementMechanism: ["SIEM Alerts", "Automated Escalation", "Incident Management System"],

    activeExceptions: [],
    recentViolations: [],

    metrics: {
      complianceRate: 95.8,
      violationCount: 0,
      exceptionCount: 0,
      lastAssessmentDate: new Date("2024-01-15T14:00:00Z"),
      nextReviewDate: new Date("2024-04-15T14:00:00Z"),
      attestationRate: 89.3,
      enforcementEffectiveness: 94.2
    },

    entityId: "auris-hq",
    versions: [],

    blockchainStatus: "verified",
    blockchainHash: "0x3c4d5e6f7a8b9c0d1e2f3a4b5c6d7e8f9a0b1c2d3e4f5a6b7c8d9e0f1a2b3c4d",
    blockchainBlock: "0x345678",
    verificationTimestamp: new Date("2024-01-20T17:35:00Z"),

    tags: ["security", "incident-response", "emergency", "operations"],
    isArchived: false,
    createdBy: "<EMAIL>",
    lastModifiedBy: "<EMAIL>"
  },
  {
    id: "pol-004",
    title: "Access Control and Identity Management Policy",
    description: "Comprehensive policy governing user access, identity management, and privileged account administration",
    policyType: "security",
    status: "under-review",
    priority: "high",
    version: "2.3",

    owner: "Identity Manager",
    ownerEmail: "<EMAIL>",
    responsibleParty: "Identity and Access Management Team",
    businessFunction: "Identity Management",

    createdDate: new Date("2023-02-01T09:00:00Z"),
    lastModifiedDate: new Date("2024-01-25T13:20:00Z"),
    lastReviewDate: new Date("2024-01-20T11:00:00Z"),
    nextReviewDate: new Date("2024-07-20T11:00:00Z"),
    reviewFrequency: "semi-annually",

    approvals: [
      {
        id: "app-004",
        approverId: "ciso-001",
        approverName: "John Smith",
        approverRole: "Chief Information Security Officer",
        status: "pending",
        comments: "Under review - pending approval of zero-trust updates"
      }
    ],
    currentApprovalStatus: "pending",

    content: "# Access Control and Identity Management Policy\n\n## Purpose\nThis policy establishes standards for user access management...",
    summary: "Defines access control principles, identity lifecycle management, and privileged access controls",
    scope: "All user accounts, system access, and identity management processes",
    applicability: ["All Users", "System Administrators", "HR Team", "IT Operations"],
    attachments: [],
    relatedPolicies: ["pol-001", "pol-003"],

    applicableFrameworks: ["NIST CSF 2.0", "ISO 27001", "SOC 2"],
    mappedControls: [],
    complianceLevel: "partially-compliant",

    enforcementStatus: "partially-enforced",
    enforcementMechanism: ["Active Directory", "RBAC Systems", "PAM Solutions"],

    activeExceptions: [],
    recentViolations: [
      {
        id: "viol-002",
        policyId: "pol-004",
        violationType: "procedural",
        severity: "low",
        description: "User access review not completed within required timeframe",
        detectedDate: new Date("2024-01-22T09:00:00Z"),
        detectedBy: "manual",
        remediationStatus: "in-progress",
        remediationDueDate: new Date("2024-02-05T17:00:00Z"),
        assignedTo: "<EMAIL>"
      }
    ],

    metrics: {
      complianceRate: 87.3,
      violationCount: 1,
      exceptionCount: 0,
      lastAssessmentDate: new Date("2024-01-20T11:00:00Z"),
      nextReviewDate: new Date("2024-07-20T11:00:00Z"),
      attestationRate: 92.1,
      enforcementEffectiveness: 88.7
    },

    entityId: "auris-hq",
    versions: [],

    blockchainStatus: "pending",

    tags: ["security", "access-control", "identity", "zero-trust"],
    isArchived: false,
    createdBy: "<EMAIL>",
    lastModifiedBy: "<EMAIL>"
  }
]

// Sample policy templates
export const samplePolicyTemplates: PolicyTemplate[] = [
  {
    id: "tpl-001",
    name: "Information Security Policy Template",
    description: "Standard template for information security policies based on ISO 27001 and NIST frameworks",
    policyType: "security",
    templateContent: "# Information Security Policy\n\n## Purpose\n[Define the purpose of this policy]\n\n## Scope\n[Define the scope and applicability]...",
    applicableFrameworks: ["ISO 27001", "NIST CSF 2.0", "SOC 2"],
    requiredFields: ["title", "owner", "scope", "purpose"],
    optionalFields: ["attachments", "exceptions", "customFields"],
    isOfficial: true,
    source: "Auris Compliance",
    usageCount: 15,
    createdAt: new Date("2023-01-01T00:00:00Z"),
    updatedAt: new Date("2024-01-01T00:00:00Z"),
    tags: ["security", "template", "iso27001", "nist"]
  }
]

// Policy statistics for dashboard
export const policyStats = {
  totalPolicies: samplePolicies.length,
  policiesByType: {
    security: samplePolicies.filter(p => p.policyType === "security").length,
    privacy: samplePolicies.filter(p => p.policyType === "privacy").length,
    compliance: samplePolicies.filter(p => p.policyType === "compliance").length,
    operational: samplePolicies.filter(p => p.policyType === "operational").length,
    hr: samplePolicies.filter(p => p.policyType === "hr").length,
    financial: samplePolicies.filter(p => p.policyType === "financial").length,
    it: samplePolicies.filter(p => p.policyType === "it").length,
    "data-governance": samplePolicies.filter(p => p.policyType === "data-governance").length,
    "risk-management": samplePolicies.filter(p => p.policyType === "risk-management").length,
    "business-continuity": samplePolicies.filter(p => p.policyType === "business-continuity").length
  },
  policiesByStatus: {
    draft: samplePolicies.filter(p => p.status === "draft").length,
    "under-review": samplePolicies.filter(p => p.status === "under-review").length,
    approved: samplePolicies.filter(p => p.status === "approved").length,
    published: samplePolicies.filter(p => p.status === "published").length,
    archived: samplePolicies.filter(p => p.status === "archived").length,
    expired: samplePolicies.filter(p => p.status === "expired").length,
    suspended: samplePolicies.filter(p => p.status === "suspended").length
  },
  overallComplianceRate: samplePolicies.reduce((acc, p) => acc + p.metrics.complianceRate, 0) / samplePolicies.length,
  totalViolations: samplePolicies.reduce((acc, p) => acc + p.metrics.violationCount, 0),
  totalExceptions: samplePolicies.reduce((acc, p) => acc + p.metrics.exceptionCount, 0),
  averageAttestationRate: samplePolicies.reduce((acc, p) => acc + p.metrics.attestationRate, 0) / samplePolicies.length
}
