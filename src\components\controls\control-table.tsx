"use client"

import React from "react"
import Link from "next/link"
import { 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Download, 
  Archive, 
  Shield, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Search,
  Filter,
  SortAsc,
  Target,
  Zap
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { Control, ImplementationStatus, TestingStatus, RiskLevel } from "@/types/controls"
import { cn } from "@/lib/utils"

interface ControlTableProps {
  controls: Control[]
}

export function ControlTable({ controls }: ControlTableProps) {
  const [searchTerm, setSearchTerm] = React.useState("")
  const [implementationFilter, setImplementationFilter] = React.useState<string>("all")
  const [testingFilter, setTestingFilter] = React.useState<string>("all")
  const [riskFilter, setRiskFilter] = React.useState<string>("all")
  const [familyFilter, setFamilyFilter] = React.useState<string>("all")
  const [sortBy, setSortBy] = React.useState<string>("priority")

  // Filter and sort controls
  const filteredControls = React.useMemo(() => {
    let filtered = controls.filter(control => {
      const matchesSearch = control.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           control.controlId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           control.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           control.ownership.primaryOwner.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesImplementation = implementationFilter === "all" || control.implementationStatus === implementationFilter
      const matchesTesting = testingFilter === "all" || control.testingStatus === testingFilter
      const matchesRisk = riskFilter === "all" || control.riskLevel === riskFilter
      const matchesFamily = familyFilter === "all" || control.controlFamily === familyFilter
      
      return matchesSearch && matchesImplementation && matchesTesting && matchesRisk && matchesFamily
    })

    // Sort controls
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "controlId":
          return a.controlId.localeCompare(b.controlId)
        case "title":
          return a.title.localeCompare(b.title)
        case "implementation":
          return a.implementationStatus.localeCompare(b.implementationStatus)
        case "testing":
          return a.testingStatus.localeCompare(b.testingStatus)
        case "effectiveness":
          return b.metrics.effectivenessScore - a.metrics.effectivenessScore
        case "risk":
          const riskOrder = { critical: 4, high: 3, medium: 2, low: 1 }
          return riskOrder[b.riskLevel] - riskOrder[a.riskLevel]
        case "priority":
        default:
          return a.priority - b.priority
      }
    })

    return filtered
  }, [controls, searchTerm, implementationFilter, testingFilter, riskFilter, familyFilter, sortBy])

  const getImplementationBadge = (status: ImplementationStatus) => {
    const statusConfig = {
      "not-implemented": { variant: "destructive" as const, label: "Not Implemented" },
      "partially-implemented": { variant: "outline" as const, label: "Partial" },
      "implemented": { variant: "default" as const, label: "Implemented" },
      "not-applicable": { variant: "secondary" as const, label: "N/A" },
      "inherited": { variant: "secondary" as const, label: "Inherited" }
    }
    
    const config = statusConfig[status]
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const getTestingBadge = (status: TestingStatus) => {
    const statusConfig = {
      "not-tested": { variant: "secondary" as const, label: "Not Tested" },
      "scheduled": { variant: "outline" as const, label: "Scheduled" },
      "in-progress": { variant: "outline" as const, label: "In Progress" },
      "passed": { variant: "default" as const, label: "Passed" },
      "failed": { variant: "destructive" as const, label: "Failed" },
      "conditional-pass": { variant: "outline" as const, label: "Conditional" },
      "not-applicable": { variant: "secondary" as const, label: "N/A" }
    }
    
    const config = statusConfig[status]
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const getRiskBadge = (risk: RiskLevel) => {
    const riskConfig = {
      critical: { variant: "destructive" as const, label: "Critical" },
      high: { variant: "destructive" as const, label: "High" },
      medium: { variant: "outline" as const, label: "Medium" },
      low: { variant: "secondary" as const, label: "Low" }
    }
    
    const config = riskConfig[risk]
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const getEffectivenessIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-4 w-4 text-green-600" />
    if (score >= 70) return <Target className="h-4 w-4 text-yellow-600" />
    return <AlertTriangle className="h-4 w-4 text-red-600" />
  }

  return (
    <div className="space-y-4">
      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search controls..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={implementationFilter} onValueChange={setImplementationFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Implementation" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Implementation</SelectItem>
            <SelectItem value="not-implemented">Not Implemented</SelectItem>
            <SelectItem value="partially-implemented">Partial</SelectItem>
            <SelectItem value="implemented">Implemented</SelectItem>
            <SelectItem value="not-applicable">N/A</SelectItem>
            <SelectItem value="inherited">Inherited</SelectItem>
          </SelectContent>
        </Select>

        <Select value={testingFilter} onValueChange={setTestingFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Testing Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Testing</SelectItem>
            <SelectItem value="not-tested">Not Tested</SelectItem>
            <SelectItem value="scheduled">Scheduled</SelectItem>
            <SelectItem value="in-progress">In Progress</SelectItem>
            <SelectItem value="passed">Passed</SelectItem>
            <SelectItem value="failed">Failed</SelectItem>
            <SelectItem value="conditional-pass">Conditional</SelectItem>
            <SelectItem value="not-applicable">N/A</SelectItem>
          </SelectContent>
        </Select>

        <Select value={riskFilter} onValueChange={setRiskFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Risk Level" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Risk Levels</SelectItem>
            <SelectItem value="critical">Critical</SelectItem>
            <SelectItem value="high">High</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="low">Low</SelectItem>
          </SelectContent>
        </Select>

        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="priority">Priority</SelectItem>
            <SelectItem value="controlId">Control ID</SelectItem>
            <SelectItem value="title">Title</SelectItem>
            <SelectItem value="implementation">Implementation</SelectItem>
            <SelectItem value="testing">Testing Status</SelectItem>
            <SelectItem value="effectiveness">Effectiveness</SelectItem>
            <SelectItem value="risk">Risk Level</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>
          Showing {filteredControls.length} of {controls.length} controls
        </span>
      </div>

      {/* Controls Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Control ID/Name</TableHead>
              <TableHead>Control Family</TableHead>
              <TableHead>Implementation</TableHead>
              <TableHead>Testing Status</TableHead>
              <TableHead>Framework Mapping</TableHead>
              <TableHead>Risk Level</TableHead>
              <TableHead>Effectiveness</TableHead>
              <TableHead>Owner</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredControls.map((control) => (
              <TableRow key={control.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <Shield className="h-4 w-4 text-muted-foreground" />
                    <div className="space-y-1">
                      <Link
                        href={`/controls/${control.id}`}
                        className="font-medium text-left hover:text-primary cursor-pointer"
                      >
                        {control.controlId}
                      </Link>
                      <p className="text-sm text-muted-foreground">
                        {control.title}
                      </p>
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  <Badge variant="outline" className="capitalize">
                    {control.controlFamily.replace('-', ' ')}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  {getImplementationBadge(control.implementationStatus)}
                </TableCell>
                
                <TableCell>
                  {getTestingBadge(control.testingStatus)}
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1">
                    <div className="font-medium text-sm">{control.primaryFramework}</div>
                    <div className="text-xs text-muted-foreground">
                      +{control.frameworkMappings.length - 1} more
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  {getRiskBadge(control.riskLevel)}
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getEffectivenessIcon(control.metrics.effectivenessScore)}
                    <span className="text-sm font-medium">
                      {control.metrics.effectivenessScore.toFixed(1)}%
                    </span>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div>
                    <div className="font-medium text-sm">{control.ownership.primaryOwner}</div>
                    <div className="text-xs text-muted-foreground">
                      {control.ownership.businessOwner}
                    </div>
                  </div>
                </TableCell>
                
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem asChild>
                        <Link href={`/controls/${control.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Control
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Download className="mr-2 h-4 w-4" />
                        Export Evidence
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Archive className="mr-2 h-4 w-4" />
                        Archive
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      {filteredControls.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No controls found matching your criteria.</p>
          <Button asChild className="mt-4">
            <Link href="/controls/create">
              <Shield className="h-4 w-4 mr-2" />
              Create Your First Control
            </Link>
          </Button>
        </div>
      )}
    </div>
  )
}
