"use client"

import React from "react"
import Link from "next/link"
import { 
  Calendar, 
  User, 
  Shield, 
  FileText, 
  AlertTriangle, 
  CheckCircle, 
  Download,
  ExternalLink,
  Clock,
  Activity,
  Settings,
  Users,
  BarChart3,
  Archive,
  Target,
  Zap,
  Database
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"

import { Control } from "@/types/controls"
import { cn } from "@/lib/utils"

interface ControlDetailViewProps {
  control: Control
  onClose: () => void
  onEdit?: (control: Control) => void
  onDownload?: (control: Control) => void
  onArchive?: (control: Control) => void
  className?: string
}

export function ControlDetailView({
  control,
  onClose,
  onEdit,
  onDownload,
  onArchive,
  className
}: ControlDetailViewProps) {
  const getImplementationColor = (status: string) => {
    switch (status) {
      case "implemented": return "text-green-600"
      case "partially-implemented": return "text-yellow-600"
      case "not-implemented": return "text-red-600"
      case "inherited": return "text-blue-600"
      case "not-applicable": return "text-gray-600"
      default: return "text-gray-600"
    }
  }

  const getTestingColor = (status: string) => {
    switch (status) {
      case "passed": return "text-green-600"
      case "failed": return "text-red-600"
      case "in-progress": return "text-yellow-600"
      case "scheduled": return "text-blue-600"
      case "not-tested": return "text-gray-600"
      default: return "text-gray-600"
    }
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "critical": return "text-red-600"
      case "high": return "text-orange-600"
      case "medium": return "text-yellow-600"
      case "low": return "text-green-600"
      default: return "text-gray-600"
    }
  }

  return (
    <div className={cn("space-y-6", className)}>
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="implementation">Implementation</TabsTrigger>
          <TabsTrigger value="testing">Testing</TabsTrigger>
          <TabsTrigger value="frameworks">Frameworks</TabsTrigger>
          <TabsTrigger value="evidence">Evidence</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Control Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Control Family</span>
                    <Badge variant="outline" className="capitalize">
                      {control.controlFamily.replace('-', ' ')}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Control Type</span>
                    <Badge variant="outline" className="capitalize">
                      {control.controlType}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Risk Level</span>
                    <Badge variant="outline" className={cn("capitalize", getRiskColor(control.riskLevel))}>
                      {control.riskLevel}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Priority</span>
                    <span className="text-sm font-medium">{control.priority}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Business Criticality</span>
                    <Badge variant="outline" className="capitalize">
                      {control.businessCriticality}
                    </Badge>
                  </div>
                </div>

                <Separator />

                <div className="grid gap-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Primary Owner</span>
                    <span className="text-sm font-medium">{control.ownership.primaryOwner}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Business Owner</span>
                    <span className="text-sm">{control.ownership.businessOwner}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Technical Owner</span>
                    <span className="text-sm">{control.ownership.technicalOwner}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Implementation Rate</span>
                      <span className="font-medium">{control.metrics.implementationRate.toFixed(1)}%</span>
                    </div>
                    <Progress value={control.metrics.implementationRate} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Effectiveness Score</span>
                      <span className="font-medium">{control.metrics.effectivenessScore.toFixed(1)}%</span>
                    </div>
                    <Progress value={control.metrics.effectivenessScore} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Automation Level</span>
                      <span className="font-medium">{control.metrics.automationLevel.toFixed(1)}%</span>
                    </div>
                    <Progress value={control.metrics.automationLevel} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Evidence Completeness</span>
                      <span className="font-medium">{control.metrics.evidenceCompleteness.toFixed(1)}%</span>
                    </div>
                    <Progress value={control.metrics.evidenceCompleteness} className="h-2" />
                  </div>
                </div>

                <Separator />

                <div className="grid gap-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Open Findings</span>
                    <Badge variant={control.metrics.openFindingsCount > 0 ? "destructive" : "secondary"}>
                      {control.metrics.openFindingsCount}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Risk Reduction</span>
                    <span className="text-sm font-medium">{control.metrics.riskReduction}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Implementation Guidance */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Implementation Guidance</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">{control.implementationGuidance}</p>
              
              {control.implementationNotes && (
                <>
                  <Separator className="my-4" />
                  <div>
                    <h4 className="text-sm font-medium mb-2">Implementation Notes</h4>
                    <p className="text-sm text-muted-foreground">{control.implementationNotes}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Automation & Monitoring */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Automation & Monitoring</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Automation Capable</span>
                    <Badge variant={control.automationCapable ? "default" : "secondary"}>
                      {control.automationCapable ? "Yes" : "No"}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Agent Monitored</span>
                    <Badge variant={control.agentMonitored ? "default" : "secondary"}>
                      {control.agentMonitored ? "Yes" : "No"}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Continuous Monitoring</span>
                    <Badge variant={control.continuousMonitoring ? "default" : "secondary"}>
                      {control.continuousMonitoring ? "Enabled" : "Disabled"}
                    </Badge>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Automation Level</span>
                    <span className="text-sm font-medium">{control.automationLevel}%</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Testing Method</span>
                    <Badge variant="outline" className="capitalize">
                      {control.testingMethod}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="implementation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Implementation Details</CardTitle>
              <CardDescription>
                Control implementation status, timeline, and requirements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Implementation Status</h4>
                    <Badge variant={
                      control.implementationStatus === "implemented" ? "default" :
                      control.implementationStatus === "partially-implemented" ? "outline" :
                      "destructive"
                    } className="capitalize">
                      {control.implementationStatus.replace('-', ' ')}
                    </Badge>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Implementation Date</h4>
                    <span className="text-sm text-muted-foreground">
                      {control.implementationDate ? new Date(control.implementationDate).toLocaleDateString() : 'Not implemented'}
                    </span>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Implementation Team</h4>
                  <div className="flex flex-wrap gap-2">
                    {control.ownership.implementationTeam.map((member) => (
                      <Badge key={member} variant="secondary">
                        {member.split('@')[0]}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Required Evidence</h4>
                  <div className="space-y-1">
                    {control.requiredEvidence.map((evidence) => (
                      <div key={evidence} className="text-sm text-muted-foreground">
                        • {evidence}
                      </div>
                    ))}
                  </div>
                </div>
                
                {control.documentationLinks.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="text-sm font-medium mb-2">Documentation Links</h4>
                      <div className="space-y-1">
                        {control.documentationLinks.map((link) => (
                          <div key={link} className="flex items-center gap-2">
                            <ExternalLink className="h-3 w-3" />
                            <Link href={link} className="text-sm text-primary hover:underline">
                              {link}
                            </Link>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="testing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Testing Information</CardTitle>
              <CardDescription>
                Control testing status, schedule, and results
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Testing Status</h4>
                    <Badge variant={
                      control.testingStatus === "passed" ? "default" :
                      control.testingStatus === "failed" ? "destructive" :
                      "outline"
                    } className="capitalize">
                      {control.testingStatus.replace('-', ' ')}
                    </Badge>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Last Test Date</h4>
                    <span className="text-sm text-muted-foreground">
                      {control.lastTestDate ? new Date(control.lastTestDate).toLocaleDateString() : 'Never tested'}
                    </span>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Next Test Date</h4>
                    <span className="text-sm text-muted-foreground">
                      {control.nextTestDate ? new Date(control.nextTestDate).toLocaleDateString() : 'Not scheduled'}
                    </span>
                  </div>
                </div>
                
                <Separator />
                
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="text-sm font-medium mb-2">Testing Frequency</h4>
                    <Badge variant="outline" className="capitalize">
                      {control.testingFrequency}
                    </Badge>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Testing Method</h4>
                    <Badge variant="outline" className="capitalize">
                      {control.testingMethod}
                    </Badge>
                  </div>
                </div>
                
                {control.testRecords.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="text-sm font-medium mb-2">Recent Test Records</h4>
                      <div className="space-y-2">
                        {control.testRecords.slice(0, 3).map((record) => (
                          <div key={record.id} className="p-3 border rounded">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="text-sm font-medium">{record.testMethod}</p>
                                <p className="text-xs text-muted-foreground">
                                  {new Date(record.testDate).toLocaleDateString()} by {record.tester.split('@')[0]}
                                </p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  {record.testResults}
                                </p>
                              </div>
                              <Badge variant={record.testStatus === "passed" ? "default" : "destructive"}>
                                {record.testStatus}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="frameworks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Framework Mappings</CardTitle>
              <CardDescription>
                Control mappings across different compliance frameworks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Primary Framework</h4>
                  <Badge variant="default">{control.primaryFramework}</Badge>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Framework Mappings</h4>
                  <div className="space-y-3">
                    {control.frameworkMappings.map((mapping) => (
                      <div key={mapping.frameworkId} className="p-3 border rounded">
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="text-sm font-medium">{mapping.frameworkName}</p>
                            <p className="text-sm text-muted-foreground">
                              {mapping.controlId} - {mapping.controlTitle}
                            </p>
                            {mapping.oscalCatalog && (
                              <p className="text-xs text-muted-foreground mt-1">
                                OSCAL Catalog: {mapping.oscalCatalog}
                              </p>
                            )}
                          </div>
                          <div className="text-right">
                            <Badge variant="outline">
                              {mapping.mappingStrength}% match
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                {control.complianceMapping.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="text-sm font-medium mb-2">Regulatory Requirements</h4>
                      <div className="flex flex-wrap gap-2">
                        {control.complianceMapping.map((requirement) => (
                          <Badge key={requirement} variant="secondary">
                            {requirement}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="evidence" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Evidence Collection</CardTitle>
              <CardDescription>
                Control evidence, documentation, and verification status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {control.evidence.length > 0 ? (
                  <div>
                    <h4 className="text-sm font-medium mb-2">Collected Evidence</h4>
                    <div className="space-y-2">
                      {control.evidence.map((evidence) => (
                        <div key={evidence.id} className="flex items-center justify-between p-3 border rounded">
                          <div className="flex items-center gap-3">
                            <FileText className="h-4 w-4 text-muted-foreground" />
                            <div>
                              <p className="text-sm font-medium">{evidence.title}</p>
                              <p className="text-xs text-muted-foreground">
                                {evidence.description}
                              </p>
                              <div className="flex items-center gap-2 mt-1">
                                <Badge variant={evidence.verificationStatus === "verified" ? "default" : "outline"}>
                                  {evidence.verificationStatus}
                                </Badge>
                                {evidence.blockchainHash && (
                                  <Badge variant="secondary" className="text-xs">
                                    Blockchain Verified
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-sm text-muted-foreground">No evidence collected yet</p>
                    <Button className="mt-4" size="sm">
                      Upload Evidence
                    </Button>
                  </div>
                )}
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Required Evidence</h4>
                  <div className="space-y-1">
                    {control.requiredEvidence.map((evidence) => (
                      <div key={evidence} className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-muted"></div>
                        <span className="text-sm text-muted-foreground">{evidence}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Control History</CardTitle>
              <CardDescription>
                Control audit trail and blockchain verification
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {control.blockchainStatus === "verified" && (
                  <div className="p-3 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800 dark:text-green-200">
                        Blockchain Verified
                      </span>
                    </div>
                    <div className="text-xs text-green-700 dark:text-green-300 space-y-1">
                      <div>Hash: <code className="bg-green-100 dark:bg-green-900/30 px-1 rounded">{control.blockchainHash}</code></div>
                      <div>Block: <code className="bg-green-100 dark:bg-green-900/30 px-1 rounded">{control.blockchainBlock}</code></div>
                      <div>Verified: {control.verificationTimestamp ? new Date(control.verificationTimestamp).toLocaleString() : 'N/A'}</div>
                    </div>
                  </div>
                )}
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Control Timeline</h4>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                      <div>
                        <p className="text-sm font-medium">Control Created</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(control.createdDate).toLocaleDateString()} by {control.createdBy.split('@')[0]}
                        </p>
                      </div>
                    </div>
                    
                    {control.implementationDate && (
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        <div>
                          <p className="text-sm font-medium">Implementation Completed</p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(control.implementationDate).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    )}
                    
                    {control.lastTestDate && (
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                        <div>
                          <p className="text-sm font-medium">Last Testing</p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(control.lastTestDate).toLocaleDateString()} - {control.testingStatus}
                          </p>
                        </div>
                      </div>
                    )}
                    
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                      <div>
                        <p className="text-sm font-medium">Last Modified</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(control.lastModifiedDate).toLocaleDateString()} by {control.lastModifiedBy.split('@')[0]}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                
                {control.auditTrail.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="text-sm font-medium mb-2">Audit Trail</h4>
                      <div className="space-y-2">
                        {control.auditTrail.map((entry) => (
                          <div key={entry.id} className="p-3 border rounded">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="text-sm font-medium capitalize">{entry.action.replace('-', ' ')}</p>
                                <p className="text-xs text-muted-foreground">
                                  {entry.description}
                                </p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  {new Date(entry.performedDate).toLocaleString()} by {entry.performedBy.split('@')[0]}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
