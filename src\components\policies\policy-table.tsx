"use client"

import React from "react"
import Link from "next/link"
import { 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Download, 
  Archive, 
  Shield, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Search,
  Filter,
  SortAsc,
  FileText
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { Policy, PolicyType, PolicyStatus } from "@/types/policies"
import { cn } from "@/lib/utils"

interface PolicyTableProps {
  policies: Policy[]
}

export function PolicyTable({ policies }: PolicyTableProps) {
  const [searchTerm, setSearchTerm] = React.useState("")
  const [statusFilter, setStatusFilter] = React.useState<string>("all")
  const [typeFilter, setTypeFilter] = React.useState<string>("all")
  const [sortBy, setSortBy] = React.useState<string>("lastModified")

  // Filter and sort policies
  const filteredPolicies = React.useMemo(() => {
    let filtered = policies.filter(policy => {
      const matchesSearch = policy.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           policy.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           policy.owner.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesStatus = statusFilter === "all" || policy.status === statusFilter
      const matchesType = typeFilter === "all" || policy.policyType === typeFilter
      
      return matchesSearch && matchesStatus && matchesType
    })

    // Sort policies
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "title":
          return a.title.localeCompare(b.title)
        case "status":
          return a.status.localeCompare(b.status)
        case "priority":
          const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 }
          return priorityOrder[b.priority] - priorityOrder[a.priority]
        case "compliance":
          return b.metrics.complianceRate - a.metrics.complianceRate
        case "lastModified":
        default:
          return new Date(b.lastModifiedDate).getTime() - new Date(a.lastModifiedDate).getTime()
      }
    })

    return filtered
  }, [policies, searchTerm, statusFilter, typeFilter, sortBy])

  const getStatusBadge = (status: PolicyStatus) => {
    const statusConfig = {
      draft: { variant: "secondary" as const, label: "Draft" },
      "under-review": { variant: "outline" as const, label: "Under Review" },
      approved: { variant: "default" as const, label: "Approved" },
      published: { variant: "default" as const, label: "Published" },
      archived: { variant: "secondary" as const, label: "Archived" },
      expired: { variant: "destructive" as const, label: "Expired" },
      suspended: { variant: "destructive" as const, label: "Suspended" }
    }
    
    const config = statusConfig[status]
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      critical: { variant: "destructive" as const, label: "Critical" },
      high: { variant: "destructive" as const, label: "High" },
      medium: { variant: "outline" as const, label: "Medium" },
      low: { variant: "secondary" as const, label: "Low" }
    }
    
    const config = priorityConfig[priority as keyof typeof priorityConfig]
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const getComplianceIcon = (complianceRate: number) => {
    if (complianceRate >= 95) return <CheckCircle className="h-4 w-4 text-green-600" />
    if (complianceRate >= 80) return <AlertTriangle className="h-4 w-4 text-yellow-600" />
    return <AlertTriangle className="h-4 w-4 text-red-600" />
  }

  const getEnforcementIcon = (status: string) => {
    switch (status) {
      case "fully-enforced":
        return <Shield className="h-4 w-4 text-green-600" />
      case "partially-enforced":
        return <Shield className="h-4 w-4 text-yellow-600" />
      case "not-enforced":
        return <Shield className="h-4 w-4 text-red-600" />
      default:
        return <Shield className="h-4 w-4 text-gray-400" />
    }
  }

  return (
    <div className="space-y-4">
      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search policies..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="under-review">Under Review</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="published">Published</SelectItem>
            <SelectItem value="archived">Archived</SelectItem>
            <SelectItem value="expired">Expired</SelectItem>
            <SelectItem value="suspended">Suspended</SelectItem>
          </SelectContent>
        </Select>

        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="security">Security</SelectItem>
            <SelectItem value="privacy">Privacy</SelectItem>
            <SelectItem value="compliance">Compliance</SelectItem>
            <SelectItem value="operational">Operational</SelectItem>
            <SelectItem value="hr">HR</SelectItem>
            <SelectItem value="financial">Financial</SelectItem>
            <SelectItem value="it">IT</SelectItem>
            <SelectItem value="data-governance">Data Governance</SelectItem>
            <SelectItem value="risk-management">Risk Management</SelectItem>
            <SelectItem value="business-continuity">Business Continuity</SelectItem>
          </SelectContent>
        </Select>

        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="lastModified">Last Modified</SelectItem>
            <SelectItem value="title">Title</SelectItem>
            <SelectItem value="status">Status</SelectItem>
            <SelectItem value="priority">Priority</SelectItem>
            <SelectItem value="compliance">Compliance Rate</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>
          Showing {filteredPolicies.length} of {policies.length} policies
        </span>
      </div>

      {/* Policies Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Policy Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Priority</TableHead>
              <TableHead>Owner</TableHead>
              <TableHead>Compliance</TableHead>
              <TableHead>Enforcement</TableHead>
              <TableHead>Last Modified</TableHead>
              <TableHead>Next Review</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPolicies.map((policy) => (
              <TableRow key={policy.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <div className="space-y-1">
                      <Link
                        href={`/policies/${policy.id}`}
                        className="font-medium text-left hover:text-primary cursor-pointer"
                      >
                        {policy.title}
                      </Link>
                      <p className="text-xs text-muted-foreground">
                        v{policy.version}
                      </p>
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  <Badge variant="outline" className="capitalize">
                    {policy.policyType.replace('-', ' ')}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  {getStatusBadge(policy.status)}
                </TableCell>
                
                <TableCell>
                  {getPriorityBadge(policy.priority)}
                </TableCell>
                
                <TableCell>
                  <div>
                    <div className="font-medium">{policy.owner}</div>
                    <div className="text-xs text-muted-foreground">
                      {policy.businessFunction}
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getComplianceIcon(policy.metrics.complianceRate)}
                    <span className="text-sm font-medium">
                      {policy.metrics.complianceRate.toFixed(1)}%
                    </span>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getEnforcementIcon(policy.enforcementStatus)}
                    <span className="text-xs text-muted-foreground capitalize">
                      {policy.enforcementStatus.replace('-', ' ')}
                    </span>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="text-sm">
                    {new Date(policy.lastModifiedDate).toLocaleDateString()}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    by {policy.lastModifiedBy.split('@')[0]}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="text-sm">
                    {new Date(policy.nextReviewDate).toLocaleDateString()}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {policy.reviewFrequency}
                  </div>
                </TableCell>
                
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem asChild>
                        <Link href={`/policies/${policy.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Policy
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Archive className="mr-2 h-4 w-4" />
                        Archive
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      {filteredPolicies.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No policies found matching your criteria.</p>
          <Button asChild className="mt-4">
            <Link href="/policies/create">
              <FileText className="h-4 w-4 mr-2" />
              Create Your First Policy
            </Link>
          </Button>
        </div>
      )}
    </div>
  )
}
