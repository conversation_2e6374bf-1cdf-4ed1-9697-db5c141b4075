"use client"

import React from "react"
import Link from "next/link"
import { Plus, Shield, AlertTriangle, Clock, TrendingUp, Users, CheckCircle, Activity, BarChart3 } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"

import { ControlDashboard } from "@/components/controls/control-dashboard"
import { ControlTable } from "@/components/controls/control-table"
import { sampleControls, controlsDashboardMetrics } from "@/lib/controls-data"

export default function ControlsPage() {
  const [activeTab, setActiveTab] = React.useState("overview")

  return (
    <div className="flex flex-col h-full">
      {/* Sticky Header */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b px-4 py-4 -mx-4 -mt-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Controls</h1>
            <p className="text-muted-foreground">
              Comprehensive control management with implementation tracking, testing oversight, and effectiveness monitoring
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href="/controls/templates">
                <Shield className="mr-2 h-4 w-4" />
                Templates
              </Link>
            </Button>
            <Button asChild>
              <Link href="/controls/create">
                <Plus className="mr-2 h-4 w-4" />
                Create Control
              </Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 space-y-6 overflow-auto">
        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Controls</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{controlsDashboardMetrics.totalControls}</div>
              <p className="text-xs text-muted-foreground">
                {controlsDashboardMetrics.implementedControls} implemented, {controlsDashboardMetrics.totalControls - controlsDashboardMetrics.implementedControls} pending
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Implementation Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{controlsDashboardMetrics.implementationRate.toFixed(1)}%</div>
              <Progress value={controlsDashboardMetrics.implementationRate} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Open Findings</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{controlsDashboardMetrics.openFindings}</div>
              <p className="text-xs text-muted-foreground">
                {controlsDashboardMetrics.criticalFindings} critical findings
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Effectiveness Score</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{controlsDashboardMetrics.averageEffectivenessScore.toFixed(1)}%</div>
              <Progress value={controlsDashboardMetrics.averageEffectivenessScore} className="mt-2" />
            </CardContent>
          </Card>
        </div>

        {/* Agent Activity Monitor */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Agent Activity Monitor
            </CardTitle>
            <CardDescription>
              Real-time control monitoring and automation status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Agent Monitored</span>
                  <span className="font-medium">{controlsDashboardMetrics.agentMonitoredControls}/{controlsDashboardMetrics.totalControls}</span>
                </div>
                <Progress value={(controlsDashboardMetrics.agentMonitoredControls / controlsDashboardMetrics.totalControls) * 100} className="h-2" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Automation Level</span>
                  <span className="font-medium">{controlsDashboardMetrics.automationLevel.toFixed(1)}%</span>
                </div>
                <Progress value={controlsDashboardMetrics.automationLevel} className="h-2" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Blockchain Verified</span>
                  <span className="font-medium">{controlsDashboardMetrics.blockchainVerifiedControls}/{controlsDashboardMetrics.totalControls}</span>
                </div>
                <Progress value={(controlsDashboardMetrics.blockchainVerifiedControls / controlsDashboardMetrics.totalControls) * 100} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="controls">All Controls</TabsTrigger>
            <TabsTrigger value="testing">Testing</TabsTrigger>
            <TabsTrigger value="remediation">Remediation</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <ControlDashboard />
          </TabsContent>

          <TabsContent value="controls" className="space-y-4">
            <ControlTable controls={sampleControls} />
          </TabsContent>

          <TabsContent value="testing" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Control Testing</CardTitle>
                <CardDescription>
                  Control testing schedule, results, and effectiveness measurement
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Control testing management interface will be implemented here
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="remediation" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Control Remediation</CardTitle>
                <CardDescription>
                  Active remediation efforts and improvement tracking
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Remediation management interface will be implemented here
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
