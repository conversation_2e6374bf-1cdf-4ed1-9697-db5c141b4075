export default function SecurityEventsPage() {
  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Security Events</h1>
        <p className="text-muted-foreground">
          Security events, compliance activities, and system operations
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Security Events</h3>
          <p className="text-2xl font-bold text-red-600">12</p>
          <p className="text-sm text-muted-foreground">Last 24 hours</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Compliance Activities</h3>
          <p className="text-2xl font-bold text-blue-600">45</p>
          <p className="text-sm text-muted-foreground">Ongoing assessments</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">System Operations</h3>
          <p className="text-2xl font-bold text-green-600">Normal</p>
          <p className="text-sm text-muted-foreground">All systems operational</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Alerts</h3>
          <p className="text-2xl font-bold text-orange-600">3</p>
          <p className="text-sm text-muted-foreground">Require attention</p>
        </div>
      </div>
    </div>
  )
}
