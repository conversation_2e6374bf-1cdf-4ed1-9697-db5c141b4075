const fs = require('fs');
const path = require('path');

// Define all the missing routes that need to be created
const missingRoutes = [
  // Assets
  { path: 'assets/ot', title: 'OT Assets', description: 'Industrial control systems, SCADA, manufacturing equipment' },
  { path: 'assets/iot', title: 'IoT Devices', description: 'Connected sensors, smart devices, edge computing' },
  { path: 'assets/identities', title: 'Identities', description: 'Users, service accounts, privileged access management' },
  { path: 'assets/applications', title: 'Applications', description: 'Software inventory, cloud services, SaaS platforms' },
  { path: 'assets/vendors', title: 'Vendors', description: 'Third-party relationships, supplier risk management' },
  { path: 'assets/processes', title: 'Processes', description: 'Business workflows, operational procedures, governance processes' },
  
  // Monitor
  { path: 'monitor/siem', title: 'SIEM Dashboard', description: 'Unified SIEM dashboard with Wazuh integration' },
  { path: 'monitor/correlation', title: 'Threat Correlation', description: 'Cross-environment threat correlation (IT/OT/IoT)' },
  { path: 'monitor/alerts', title: 'Alert Prioritization', description: 'System Agent coordinated alert prioritization' },
  
  // Frameworks
  { path: 'frameworks/iso-27001', title: 'ISO 27001', description: 'Information Security Management System' },
  { path: 'frameworks/soc-2', title: 'SOC 2', description: 'Service Organization Control 2 compliance' },
  { path: 'frameworks/pci-dss', title: 'PCI DSS', description: 'Payment Card Industry Data Security Standard' },
  { path: 'frameworks/hipaa', title: 'HIPAA', description: 'Health Insurance Portability and Accountability Act' },
  { path: 'frameworks/oscal', title: 'OSCAL Modeling', description: 'OSCAL-based framework modeling and management' },
  { path: 'frameworks/harmonization', title: 'Multi-Framework', description: 'Multi-framework harmonization and control mapping' },
  
  // Controls
  { path: 'controls/implementation', title: 'Implementation', description: 'Security control implementation and tracking' },
  { path: 'controls/testing', title: 'Testing', description: 'OSCAL standardized control testing' },
  { path: 'controls/assessment', title: 'Assessment', description: 'Compliance Agent automated control assessment' },
  { path: 'controls/validation', title: 'Validation', description: 'Control validation and effectiveness measurement' },
  { path: 'controls/mapping', title: 'Mapping', description: 'Cross-framework control mapping' },
  
  // Policies
  { path: 'policies/code', title: 'Policy as Code', description: 'OPA-based policy-as-code implementation' },
  { path: 'policies/translation', title: 'Translation', description: 'Compliance Agent policy translation and application' },
  { path: 'policies/consistency', title: 'Consistency', description: 'Cross-environment policy consistency verification' },
  { path: 'policies/enforcement', title: 'Enforcement', description: 'OPA policy enforcement integration' },
  
  // Assessments
  { path: 'assessments/risk', title: 'Risk Assessments', description: 'Automated risk assessments and gap analysis' },
  { path: 'assessments/testing', title: 'Control Testing', description: 'Assessment Agent orchestrated control testing' },
  { path: 'assessments/gaps', title: 'Gap Analysis', description: 'Compliance gap identification and analysis' },
  { path: 'assessments/quantitative', title: 'Quantitative Risk', description: 'Quantitative risk analysis with Open Source Risk Engine' },
  { path: 'assessments/continuous', title: 'Continuous Assessment', description: 'Continuous assessment scheduling and execution' },
  
  // Workflows
  { path: 'workflows/automation', title: 'Process Automation', description: 'Business process automation with Flowable engine' },
  { path: 'workflows/optimization', title: 'Optimization', description: 'Workflow Agent orchestrated process optimization' },
  { path: 'workflows/design', title: 'Design', description: 'Custom workflow design and template management' },
  { path: 'workflows/integration', title: 'Integration', description: 'Integration with compliance and security processes' },
  { path: 'workflows/analytics', title: 'Analytics', description: 'Workflow performance analytics' },
  
  // Remediation
  { path: 'remediation/incidents', title: 'Incident Response', description: 'Incident response and security remediation coordination' },
  { path: 'remediation/automated', title: 'Automated Response', description: 'Remediation Agent automated response orchestration' },
  { path: 'remediation/dfir', title: 'DFIR Integration', description: 'DFIR-IRIS integration for structured investigation' },
  { path: 'remediation/cross-env', title: 'Cross-Environment', description: 'Cross-environment remediation tracking and validation' },
  { path: 'remediation/validation', title: 'Validation', description: 'Remediation validation and testing' },
  
  // Reports
  { path: 'reports/compliance', title: 'Compliance Documentation', description: 'Reporting Agent generated compliance documentation' },
  { path: 'reports/dashboards', title: 'Interactive Dashboards', description: 'Interactive dashboards and static report artifacts' },
  { path: 'reports/executive', title: 'Executive Summaries', description: 'Executive summaries and regulatory submission preparation' },
  { path: 'reports/realtime', title: 'Real-time Status', description: 'Real-time compliance status and trend analysis' },
  { path: 'reports/audit', title: 'Audit Reports', description: 'Audit preparation and reports' },
  
  // Artifacts
  { path: 'artifacts/evidence', title: 'Evidence Repository', description: 'Blockchain-secured evidence repository' },
  { path: 'artifacts/documentation', title: 'Documentation Storage', description: 'Immutable compliance documentation storage' },
  { path: 'artifacts/verification', title: 'Verification', description: 'Cryptographic verification of all security artifacts' },
  { path: 'artifacts/audit', title: 'Audit-Ready Evidence', description: 'Audit-ready evidence with tamper-proof integrity' },
  { path: 'artifacts/archive', title: 'Archive Management', description: 'Evidence archival and retention management' },
  
  // Portals
  { path: 'portals/trust', title: 'Trust Portals', description: 'Stakeholder-specific trust portals and interfaces' },
  { path: 'portals/chatbots', title: 'AI Chatbots', description: 'AI-powered chatbots for audit facilitation' },
  { path: 'portals/self-service', title: 'Self-Service', description: 'Self-service compliance questionnaire completion' },
  { path: 'portals/communication', title: 'Stakeholder Communication', description: 'Secure stakeholder communication and evidence sharing' },
  { path: 'portals/management', title: 'Portal Management', description: 'Portal configuration and access management' },
];

// Function to create a page component
function createPageComponent(title, description) {
  const componentName = title.replace(/[^a-zA-Z0-9]/g, '') + 'Page';
  
  return `export default function ${componentName}() {
  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">${title}</h1>
        <p className="text-muted-foreground">
          ${description}
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Status</h3>
          <p className="text-2xl font-bold text-green-600">Active</p>
          <p className="text-sm text-muted-foreground">System operational</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Performance</h3>
          <p className="text-2xl font-bold text-blue-600">Optimal</p>
          <p className="text-sm text-muted-foreground">Running efficiently</p>
        </div>
        
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Last Updated</h3>
          <p className="text-2xl font-bold text-gray-600">Now</p>
          <p className="text-sm text-muted-foreground">Real-time data</p>
        </div>
      </div>
    </div>
  )
}`;
}

// Create directories and files
missingRoutes.forEach(route => {
  const fullPath = path.join(__dirname, '..', 'src', 'app', route.path);
  const dirPath = path.dirname(fullPath);
  const filePath = path.join(fullPath, 'page.tsx');
  
  // Create directory if it doesn't exist
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
  
  // Create page file if it doesn't exist
  if (!fs.existsSync(filePath)) {
    const content = createPageComponent(route.title, route.description);
    fs.writeFileSync(filePath, content);
    console.log(`Created: ${route.path}/page.tsx`);
  } else {
    console.log(`Exists: ${route.path}/page.tsx`);
  }
});

console.log('Finished generating missing pages!');
