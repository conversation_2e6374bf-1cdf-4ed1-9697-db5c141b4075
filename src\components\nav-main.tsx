"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

import { NavigationItem } from "@/lib/navigation-data"

export function NavMain({
  items,
}: {
  items: NavigationItem[]
}) {
  const pathname = usePathname()

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Platform</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          const isActive = pathname === item.url || pathname.startsWith(item.url + "/")

          return (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton
                asChild
                tooltip={item.title}
                className={cn(
                  "transition-all duration-200 relative",
                  "hover:bg-gray-50 dark:hover:bg-gray-800/30",
                  isActive && "bg-gray-100 text-gray-900 hover:bg-gray-150 dark:bg-gray-800/60 dark:text-gray-100 dark:hover:bg-gray-800/80 border-l-2 border-gray-400 dark:border-gray-500"
                )}
              >
                <Link href={item.url}>
                  {item.icon && (
                    <item.icon className={cn(
                      "transition-colors duration-200",
                      isActive && "text-gray-700 dark:text-gray-300"
                    )} />
                  )}
                  <span>{item.title}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
