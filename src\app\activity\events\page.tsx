export default function EventStreamPage() {
  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Event Stream</h1>
        <p className="text-muted-foreground">
          Unified event stream from all system components
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-1">
        <div className="p-6 border rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Recent Events</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded">
              <div>
                <p className="font-medium">Compliance Agent: Framework Analysis Complete</p>
                <p className="text-sm text-muted-foreground">NIST CSF 2.0 assessment finished</p>
              </div>
              <span className="text-sm text-muted-foreground">2 min ago</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded">
              <div>
                <p className="font-medium">Security Event: New Asset Detected</p>
                <p className="text-sm text-muted-foreground">Server added to IT assets inventory</p>
              </div>
              <span className="text-sm text-muted-foreground">5 min ago</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded">
              <div>
                <p className="font-medium">Workflow Agent: Process Optimization</p>
                <p className="text-sm text-muted-foreground">Incident response workflow updated</p>
              </div>
              <span className="text-sm text-muted-foreground">12 min ago</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
