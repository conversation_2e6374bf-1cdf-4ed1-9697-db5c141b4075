"use client"

import * as React from "react"
import { 
  ChevronDown, 
  ChevronUp, 
  Download, 
  Eye, 
  Shield, 
  Copy,
  ExternalLink,
  MoreHorizontal,
  CheckCircle,
  Clock,
  AlertTriangle,
  X
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Artifact, ArtifactSort } from "@/lib/artifacts-types"
import { 
  getArtifactTypeLabel, 
  getSourceModuleLabel, 
  getBlockchainStatusLabel, 
  getAccessLevelLabel,
  formatFileSize 
} from "@/lib/artifacts-data"

interface ArtifactsTableProps {
  artifacts: Artifact[]
  selectedArtifacts: Artifact[]
  onSelectionChange: (artifacts: Artifact[]) => void
  sort: ArtifactSort
  onSortChange: (sort: ArtifactSort) => void
  onViewArtifact?: (artifact: Artifact) => void
  onDownloadArtifact?: (artifact: Artifact) => void
  onVerifyArtifact?: (artifact: Artifact) => void
  className?: string
}

export function ArtifactsTable({
  artifacts,
  selectedArtifacts,
  onSelectionChange,
  sort,
  onSortChange,
  onViewArtifact,
  onDownloadArtifact,
  onVerifyArtifact,
  className
}: ArtifactsTableProps) {
  const isAllSelected = artifacts.length > 0 && selectedArtifacts.length === artifacts.length
  const isPartiallySelected = selectedArtifacts.length > 0 && selectedArtifacts.length < artifacts.length

  const handleSelectAll = () => {
    if (isAllSelected) {
      onSelectionChange([])
    } else {
      onSelectionChange(artifacts)
    }
  }

  const handleSelectArtifact = (artifact: Artifact) => {
    const isSelected = selectedArtifacts.some(a => a.id === artifact.id)
    if (isSelected) {
      onSelectionChange(selectedArtifacts.filter(a => a.id !== artifact.id))
    } else {
      onSelectionChange([...selectedArtifacts, artifact])
    }
  }

  const handleSort = (field: keyof Artifact) => {
    if (sort.field === field) {
      onSortChange({
        field,
        direction: sort.direction === "asc" ? "desc" : "asc"
      })
    } else {
      onSortChange({ field, direction: "asc" })
    }
  }

  const getSortIcon = (field: keyof Artifact) => {
    if (sort.field !== field) return null
    return sort.direction === "asc" ? 
      <ChevronUp className="h-4 w-4" /> : 
      <ChevronDown className="h-4 w-4" />
  }

  const getBlockchainStatusIcon = (status: string) => {
    switch (status) {
      case "verified":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />
      case "failed":
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <X className="h-4 w-4 text-gray-400" />
    }
  }

  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case "public":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "internal":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      case "restricted":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
      case "confidential":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const truncateHash = (hash: string | undefined) => {
    if (!hash) return "N/A"
    return `${hash.slice(0, 8)}...${hash.slice(-8)}`
  }

  const formatRelativeTime = (date: Date) => {
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return "Just now"
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    return date.toLocaleDateString()
  }

  return (
    <div className={`border rounded-lg ${className}`}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={isAllSelected}
                ref={(el) => {
                  if (el) {
                    el.indeterminate = isPartiallySelected
                  }
                }}
                onCheckedChange={handleSelectAll}
              />
            </TableHead>
            
            <TableHead 
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => handleSort("name")}
            >
              <div className="flex items-center gap-2">
                Document Name
                {getSortIcon("name")}
              </div>
            </TableHead>
            
            <TableHead 
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => handleSort("artifactType")}
            >
              <div className="flex items-center gap-2">
                Type
                {getSortIcon("artifactType")}
              </div>
            </TableHead>
            
            <TableHead 
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => handleSort("sourceModule")}
            >
              <div className="flex items-center gap-2">
                Source
                {getSortIcon("sourceModule")}
              </div>
            </TableHead>
            
            <TableHead 
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => handleSort("createdAt")}
            >
              <div className="flex items-center gap-2">
                Created
                {getSortIcon("createdAt")}
              </div>
            </TableHead>
            
            <TableHead>Verification</TableHead>
            <TableHead>Access Level</TableHead>
            <TableHead>File Info</TableHead>
            <TableHead>Hash</TableHead>
            <TableHead className="w-12"></TableHead>
          </TableRow>
        </TableHeader>
        
        <TableBody>
          {artifacts.map((artifact) => {
            const isSelected = selectedArtifacts.some(a => a.id === artifact.id)
            
            return (
              <TableRow 
                key={artifact.id}
                className={isSelected ? "bg-muted/50" : ""}
              >
                <TableCell>
                  <Checkbox
                    checked={isSelected}
                    onCheckedChange={() => handleSelectArtifact(artifact)}
                  />
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1">
                    <div 
                      className="font-medium cursor-pointer hover:text-blue-600"
                      onClick={() => onViewArtifact?.(artifact)}
                    >
                      {artifact.name}
                    </div>
                    {artifact.description && (
                      <div className="text-sm text-muted-foreground line-clamp-2">
                        {artifact.description}
                      </div>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <Badge variant="outline">
                    {getArtifactTypeLabel(artifact.artifactType)}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <div className="text-sm">
                    {getSourceModuleLabel(artifact.sourceModule)}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1">
                    <div className="text-sm">
                      {formatRelativeTime(artifact.createdAt)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {artifact.createdAt.toLocaleDateString()}
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getBlockchainStatusIcon(artifact.blockchainStatus)}
                    <span className="text-sm">
                      {getBlockchainStatusLabel(artifact.blockchainStatus)}
                    </span>
                  </div>
                </TableCell>
                
                <TableCell>
                  <Badge 
                    variant="outline" 
                    className={getAccessLevelColor(artifact.accessLevel)}
                  >
                    {getAccessLevelLabel(artifact.accessLevel)}
                  </Badge>
                </TableCell>
                
                <TableCell>
                  <div className="space-y-1">
                    <div className="text-sm">
                      {formatFileSize(artifact.fileSize)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {artifact.fileFormat}
                    </div>
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-2">
                    <code className="text-xs bg-muted px-2 py-1 rounded">
                      {truncateHash(artifact.blockchainHash)}
                    </code>
                    {artifact.blockchainHash && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(artifact.blockchainHash!)}
                        className="h-6 w-6 p-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      
                      <DropdownMenuItem onClick={() => onViewArtifact?.(artifact)}>
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </DropdownMenuItem>
                      
                      <DropdownMenuItem onClick={() => onDownloadArtifact?.(artifact)}>
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </DropdownMenuItem>
                      
                      {artifact.blockchainStatus !== "verified" && (
                        <DropdownMenuItem onClick={() => onVerifyArtifact?.(artifact)}>
                          <Shield className="h-4 w-4 mr-2" />
                          Verify
                        </DropdownMenuItem>
                      )}
                      
                      <DropdownMenuSeparator />
                      
                      <DropdownMenuItem>
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View on Blockchain
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
      
      {artifacts.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No artifacts found matching your criteria.</p>
        </div>
      )}
    </div>
  )
}
