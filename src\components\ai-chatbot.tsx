"use client"

import * as React from "react"
import { X, Send, Bot } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"

export function AIChatbot() {
  const [isOpen, setIsOpen] = React.useState(false)
  const [message, setMessage] = React.useState("")
  const panelRef = React.useRef<HTMLDivElement>(null)
  const triggerRef = React.useRef<HTMLButtonElement>(null)
  const inputRef = React.useRef<HTMLInputElement>(null)

  // Handle global keyboard shortcuts
  React.useEffect(() => {
    const handleKeyboardShortcuts = (event: KeyboardEvent) => {
      // Handle Escape key to close panel
      if (event.key === "Escape" && isOpen) {
        setIsOpen(false)
        triggerRef.current?.focus()
        return
      }

      // Handle Alt + A to toggle chatbot
      if (event.altKey && event.key.toLowerCase() === "a") {
        event.preventDefault()
        event.stopPropagation()
        
        if (isOpen) {
          // Close the panel and focus the trigger button
          setIsOpen(false)
          triggerRef.current?.focus()
        } else {
          // Open the panel and focus the input field
          setIsOpen(true)
          // Focus will be handled in the focus management effect
        }
      }
    }

    document.addEventListener("keydown", handleKeyboardShortcuts)
    return () => document.removeEventListener("keydown", handleKeyboardShortcuts)
  }, [isOpen])

  // Handle click outside to close panel
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        panelRef.current &&
        !panelRef.current.contains(event.target as Node) &&
        !triggerRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [isOpen])

  // Focus management
  React.useEffect(() => {
    if (isOpen && panelRef.current) {
      // Small delay to ensure the panel is fully rendered
      setTimeout(() => {
        // Focus the input field when opened via keyboard shortcut or button click
        if (inputRef.current) {
          inputRef.current.focus()
        } else {
          // Fallback to first focusable element
          const firstFocusable = panelRef.current?.querySelector(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          ) as HTMLElement
          firstFocusable?.focus()
        }
      }, 100)
    }
  }, [isOpen])

  const handleSendMessage = () => {
    if (message.trim()) {
      // Placeholder for sending message
      console.log("Sending message:", message)
      setMessage("")
    }
  }

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <>
      {/* Trigger Button */}
      <Button
        ref={triggerRef}
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="h-8 w-8 p-0"
        aria-label="Open AI Assistant (Alt + A)"
        aria-expanded={isOpen}
        aria-haspopup="dialog"
        title="AI Assistant (Alt + A)"
      >
        <Bot className="h-4 w-4" />
      </Button>

      {/* Backdrop Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
          aria-hidden="true"
        />
      )}

      {/* Slide-out Panel */}
      <div
        className={cn(
          "fixed top-0 right-0 h-full bg-background border-l shadow-lg z-50 transition-transform duration-300 ease-in-out",
          "w-[40%] min-w-[400px] max-w-[600px]",
          isOpen ? "translate-x-0" : "translate-x-full"
        )}
        ref={panelRef}
        role="dialog"
        aria-modal="true"
        aria-labelledby="chatbot-title"
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10">
                <Bot className="h-4 w-4 text-primary" />
              </div>
              <div>
                <h2 id="chatbot-title" className="text-lg font-semibold">
                  AI Assistant
                </h2>
                <p className="text-sm text-muted-foreground">
                  GRCOS Compliance Intelligence
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
              className="h-8 w-8 p-0"
              aria-label="Close AI Assistant"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Message Area */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 p-4 overflow-y-auto">
              <div className="space-y-4">
                {/* Welcome Message */}
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 mt-0.5">
                        <Bot className="h-3 w-3 text-primary" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm">
                          Hello! I'm your GRCOS AI Assistant. I can help you with:
                        </p>
                        <ul className="text-sm text-muted-foreground mt-2 space-y-1">
                          <li>• Policy and control guidance</li>
                          <li>• Compliance framework questions</li>
                          <li>• Risk assessment insights</li>
                          <li>• Audit preparation support</li>
                          <li>• GRCOS feature explanations</li>
                        </ul>
                        <p className="text-sm mt-3">
                          What would you like to know about your compliance posture?
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Placeholder for conversation messages */}
                <div className="text-center text-muted-foreground text-sm py-8">
                  <Bot className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p>Start a conversation to see messages here</p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Input Area */}
            <div className="p-4">
              <div className="flex gap-2">
                <Input
                  ref={inputRef}
                  placeholder="Ask me about compliance, policies, controls..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1"
                  aria-label="Message input"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!message.trim()}
                  size="sm"
                  aria-label="Send message"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Press Enter to send, Shift+Enter for new line • Alt+A to toggle
              </p>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t p-3">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Powered by Auris Compliance AI</span>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <span>Online</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
